import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DeviceService } from './device.service';
import { DeviceController } from './device.controller';
import { EdgeDevice } from './entities/edge-device.entity';

@Module({
  imports: [
    ...(process.env.ENABLE_DATABASE !== 'false' ? [
      TypeOrmModule.forFeature([EdgeDevice])
    ] : [])
  ],
  controllers: [DeviceController],
  providers: [DeviceService],
  exports: [DeviceService],
})
export class DeviceModule {}
