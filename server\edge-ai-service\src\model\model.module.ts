import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ModelService } from './model.service';
import { ModelController } from './model.controller';
import { AIModel } from './entities/ai-model.entity';

@Module({
  imports: [
    ...(process.env.ENABLE_DATABASE !== 'false' ? [
      TypeOrmModule.forFeature([AIModel])
    ] : [])
  ],
  controllers: [ModelController],
  providers: [ModelService],
  exports: [ModelService],
})
export class ModelModule {}
