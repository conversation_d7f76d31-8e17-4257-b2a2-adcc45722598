import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MonitoringService } from './monitoring.service';
import { MonitoringController } from './monitoring.controller';
import { DevicePerformance } from './entities/device-performance.entity';

@Module({
  imports: [
    ...(process.env.ENABLE_DATABASE !== 'false' ? [
      TypeOrmModule.forFeature([DevicePerformance])
    ] : [])
  ],
  controllers: [MonitoringController],
  providers: [MonitoringService],
  exports: [MonitoringService],
})
export class MonitoringModule {}
