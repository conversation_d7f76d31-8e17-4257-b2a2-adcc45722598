import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InferenceRequest, InferenceStatus } from './entities/inference-request.entity';
import { InferenceResult } from './entities/inference-result.entity';

@Injectable()
export class InferenceService {
  private readonly logger = new Logger(InferenceService.name);

  constructor(
    @InjectRepository(InferenceRequest, { optional: true })
    private readonly requestRepository?: Repository<InferenceRequest>,
    @InjectRepository(InferenceResult, { optional: true })
    private readonly resultRepository?: Repository<InferenceResult>,
  ) {}

  async createRequest(requestData: Partial<InferenceRequest>): Promise<InferenceRequest> {
    try {
      const request = this.requestRepository.create(requestData);
      const savedRequest = await this.requestRepository.save(request);
      
      this.logger.log(`推理请求创建成功: ${savedRequest.requestId}`);
      return savedRequest;
    } catch (error) {
      this.logger.error('创建推理请求失败', error);
      throw error;
    }
  }

  async updateRequestStatus(
    requestId: string, 
    status: InferenceStatus,
    deviceId?: string
  ): Promise<void> {
    try {
      const updateData: any = { status };
      
      if (status === InferenceStatus.PROCESSING) {
        updateData.startedAt = new Date();
        if (deviceId) updateData.deviceId = deviceId;
      } else if (status === InferenceStatus.COMPLETED || status === InferenceStatus.FAILED) {
        updateData.completedAt = new Date();
      }

      await this.requestRepository.update({ requestId }, updateData);
      
      this.logger.debug(`推理请求状态更新: ${requestId} -> ${status}`);
    } catch (error) {
      this.logger.error('更新推理请求状态失败', error);
      throw error;
    }
  }

  async saveResult(resultData: Partial<InferenceResult>): Promise<InferenceResult> {
    try {
      const result = this.resultRepository.create(resultData);
      const savedResult = await this.resultRepository.save(result);
      
      this.logger.log(`推理结果保存成功: ${savedResult.requestId}`);
      return savedResult;
    } catch (error) {
      this.logger.error('保存推理结果失败', error);
      throw error;
    }
  }

  async findRequestById(requestId: string): Promise<InferenceRequest> {
    return await this.requestRepository.findOne({ where: { requestId } });
  }

  async findResultByRequestId(requestId: string): Promise<InferenceResult> {
    return await this.resultRepository.findOne({ where: { requestId } });
  }

  async getInferenceStatistics(): Promise<any> {
    try {
      const total = await this.requestRepository.count();
      const completed = await this.requestRepository.count({ 
        where: { status: InferenceStatus.COMPLETED } 
      });
      const failed = await this.requestRepository.count({ 
        where: { status: InferenceStatus.FAILED } 
      });
      const pending = await this.requestRepository.count({ 
        where: { status: InferenceStatus.PENDING } 
      });

      // 计算平均处理时间
      const avgProcessingTime = await this.requestRepository
        .createQueryBuilder('request')
        .select('AVG(request.processingTime)', 'avg')
        .where('request.status = :status', { status: InferenceStatus.COMPLETED })
        .getRawOne();

      return {
        total,
        completed,
        failed,
        pending,
        successRate: total > 0 ? (completed / total) * 100 : 0,
        averageProcessingTime: avgProcessingTime?.avg || 0,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('获取推理统计失败', error);
      throw error;
    }
  }
}
