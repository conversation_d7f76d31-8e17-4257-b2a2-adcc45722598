import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EdgeDevice, DeviceStatus } from './entities/edge-device.entity';

@Injectable()
export class DeviceService {
  private readonly logger = new Logger(DeviceService.name);

  constructor(
    @InjectRepository(EdgeDevice, { optional: true })
    private readonly deviceRepository?: Repository<EdgeDevice>,
  ) {}

  async create(deviceData: Partial<EdgeDevice>): Promise<EdgeDevice> {
    try {
      if (!this.deviceRepository) {
        // 无数据库模式：返回模拟数据
        const mockDevice = {
          id: `device-${Date.now()}`,
          deviceId: deviceData.deviceId || `mock-device-${Date.now()}`,
          name: deviceData.name || '模拟设备',
          type: deviceData.type || 'industrial_pc',
          location: deviceData.location || '模拟位置',
          status: deviceData.status || 'online',
          capabilities: deviceData.capabilities || {},
          performance: deviceData.performance || {},
          networkInfo: deviceData.networkInfo || {},
          deployedModels: deviceData.deployedModels || [],
          lastHeartbeat: new Date(),
          description: deviceData.description,
          metadata: deviceData.metadata,
          createdAt: new Date(),
          updatedAt: new Date(),
        } as EdgeDevice;

        this.logger.log(`模拟设备创建成功: ${mockDevice.deviceId}`);
        return mockDevice;
      }

      const device = this.deviceRepository.create(deviceData);
      const savedDevice = await this.deviceRepository.save(device);

      this.logger.log(`设备创建成功: ${savedDevice.deviceId}`);
      return savedDevice;
    } catch (error) {
      this.logger.error('创建设备失败', error);
      throw error;
    }
  }

  async findAll(filters?: {
    status?: DeviceStatus;
    type?: string;
    location?: string;
  }): Promise<EdgeDevice[]> {
    try {
      if (!this.deviceRepository) {
        // 无数据库模式：返回模拟数据
        const mockDevices = [
          {
            id: 'device-001',
            deviceId: 'edge_001',
            name: '工业控制器-001',
            type: 'industrial_pc',
            location: '生产车间A',
            status: 'online',
            capabilities: { cpu: { cores: 4, frequency: 2400 }, memory: { total: 8192 } },
            performance: { cpuUsage: 25.5, memoryUsage: 35.2 },
            networkInfo: { ipAddress: '*************', bandwidth: 1000 },
            deployedModels: [],
            lastHeartbeat: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            id: 'device-002',
            deviceId: 'edge_002',
            name: '智能传感器-002',
            type: 'smart_sensor',
            location: '仓库B区',
            status: 'online',
            capabilities: { cpu: { cores: 2, frequency: 1800 }, memory: { total: 4096 } },
            performance: { cpuUsage: 15.2, memoryUsage: 28.7 },
            networkInfo: { ipAddress: '*************', bandwidth: 100 },
            deployedModels: [],
            lastHeartbeat: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
          }
        ] as EdgeDevice[];

        // 应用过滤器
        let filteredDevices = mockDevices;
        if (filters?.status) {
          filteredDevices = filteredDevices.filter(d => d.status === filters.status);
        }
        if (filters?.type) {
          filteredDevices = filteredDevices.filter(d => d.type === filters.type);
        }
        if (filters?.location) {
          filteredDevices = filteredDevices.filter(d => d.location.includes(filters.location));
        }

        return filteredDevices;
      }

      const queryBuilder = this.deviceRepository.createQueryBuilder('device');

      if (filters?.status) {
        queryBuilder.andWhere('device.status = :status', { status: filters.status });
      }

      if (filters?.type) {
        queryBuilder.andWhere('device.type = :type', { type: filters.type });
      }

      if (filters?.location) {
        queryBuilder.andWhere('device.location LIKE :location', {
          location: `%${filters.location}%`
        });
      }

      return await queryBuilder.getMany();
    } catch (error) {
      this.logger.error('查询设备列表失败', error);
      throw error;
    }
  }

  async findOne(id: string): Promise<EdgeDevice> {
    try {
      const device = await this.deviceRepository.findOne({ where: { id } });
      
      if (!device) {
        throw new NotFoundException(`设备不存在: ${id}`);
      }

      return device;
    } catch (error) {
      this.logger.error('查询设备失败', error);
      throw error;
    }
  }

  async findByDeviceId(deviceId: string): Promise<EdgeDevice> {
    try {
      const device = await this.deviceRepository.findOne({ 
        where: { deviceId } 
      });
      
      if (!device) {
        throw new NotFoundException(`设备不存在: ${deviceId}`);
      }

      return device;
    } catch (error) {
      this.logger.error('查询设备失败', error);
      throw error;
    }
  }

  async update(id: string, updateData: Partial<EdgeDevice>): Promise<EdgeDevice> {
    try {
      const device = await this.findOne(id);
      
      Object.assign(device, updateData);
      const updatedDevice = await this.deviceRepository.save(device);
      
      this.logger.log(`设备更新成功: ${updatedDevice.deviceId}`);
      return updatedDevice;
    } catch (error) {
      this.logger.error('更新设备失败', error);
      throw error;
    }
  }

  async updateHeartbeat(deviceId: string, performance?: any): Promise<void> {
    try {
      const updateData: any = {
        lastHeartbeat: new Date(),
        status: DeviceStatus.ONLINE,
      };

      if (performance) {
        updateData.performance = performance;
      }

      await this.deviceRepository.update(
        { deviceId },
        updateData
      );

      this.logger.debug(`设备心跳更新: ${deviceId}`);
    } catch (error) {
      this.logger.error('更新设备心跳失败', error);
      throw error;
    }
  }

  async updateStatus(deviceId: string, status: DeviceStatus): Promise<void> {
    try {
      await this.deviceRepository.update(
        { deviceId },
        { status }
      );

      this.logger.log(`设备状态更新: ${deviceId} -> ${status}`);
    } catch (error) {
      this.logger.error('更新设备状态失败', error);
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    try {
      const device = await this.findOne(id);
      await this.deviceRepository.remove(device);
      
      this.logger.log(`设备删除成功: ${device.deviceId}`);
    } catch (error) {
      this.logger.error('删除设备失败', error);
      throw error;
    }
  }

  async getDeviceStatistics(): Promise<any> {
    try {
      const total = await this.deviceRepository.count();
      const online = await this.deviceRepository.count({ 
        where: { status: DeviceStatus.ONLINE } 
      });
      const offline = await this.deviceRepository.count({ 
        where: { status: DeviceStatus.OFFLINE } 
      });
      const maintenance = await this.deviceRepository.count({ 
        where: { status: DeviceStatus.MAINTENANCE } 
      });

      // 按类型统计
      const typeStats = await this.deviceRepository
        .createQueryBuilder('device')
        .select('device.type', 'type')
        .addSelect('COUNT(*)', 'count')
        .groupBy('device.type')
        .getRawMany();

      // 按位置统计
      const locationStats = await this.deviceRepository
        .createQueryBuilder('device')
        .select('device.location', 'location')
        .addSelect('COUNT(*)', 'count')
        .groupBy('device.location')
        .getRawMany();

      return {
        total,
        status: {
          online,
          offline,
          maintenance,
        },
        byType: typeStats,
        byLocation: locationStats,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('获取设备统计失败', error);
      throw error;
    }
  }

  async checkOfflineDevices(): Promise<EdgeDevice[]> {
    try {
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      
      const offlineDevices = await this.deviceRepository
        .createQueryBuilder('device')
        .where('device.status = :status', { status: DeviceStatus.ONLINE })
        .andWhere('device.lastHeartbeat < :threshold', { threshold: fiveMinutesAgo })
        .getMany();

      // 更新离线设备状态
      if (offlineDevices.length > 0) {
        await this.deviceRepository.update(
          { id: { $in: offlineDevices.map(d => d.id) } as any },
          { status: DeviceStatus.OFFLINE }
        );

        this.logger.warn(`发现 ${offlineDevices.length} 个离线设备`);
      }

      return offlineDevices;
    } catch (error) {
      this.logger.error('检查离线设备失败', error);
      throw error;
    }
  }
}
