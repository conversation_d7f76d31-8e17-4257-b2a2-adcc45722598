import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LearningService } from './learning.service';
import { LearningController } from './learning.controller';
import { LearningTask } from './entities/learning-task.entity';

@Module({
  imports: [
    ...(process.env.ENABLE_DATABASE !== 'false' ? [
      TypeOrmModule.forFeature([LearningTask])
    ] : [])
  ],
  controllers: [LearningController],
  providers: [LearningService],
  exports: [LearningService],
})
export class LearningModule {}
